import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../data/models/resume_template_model.dart';
import '../remote_config/remote_config_cubit.dart';


class TemplateState extends Equatable {
  final List<ResumeTemplateModel> templates;
  final List<ResumeTemplateModel> filteredTemplates;
  final List<TemplateCategory> categories;
  final ResumeTemplateModel? selectedTemplate;
  final ResumeTemplateModel? previewTemplate;
  final TemplateCategory? selectedCategory;
  final bool showOnlyFreeTemplates;
  final String searchQuery;
  final bool isLoading;
  final String? errorMessage;

  const TemplateState({
    this.templates = const [],
    this.filteredTemplates = const [],
    this.categories = const [],
    this.selectedTemplate,
    this.previewTemplate,
    this.selectedCategory,
    this.showOnlyFreeTemplates = false,
    this.searchQuery = '',
    this.isLoading = false,
    this.errorMessage,
  });

  TemplateState copyWith({
    List<ResumeTemplateModel>? templates,
    List<ResumeTemplateModel>? filteredTemplates,
    List<TemplateCategory>? categories,
    ResumeTemplateModel? selectedTemplate,
    ResumeTemplateModel? previewTemplate,
    TemplateCategory? selectedCategory,
    bool? showOnlyFreeTemplates,
    String? searchQuery,
    bool? isLoading,
    String? errorMessage,
  }) {
    return TemplateState(
      templates: templates ?? this.templates,
      filteredTemplates: filteredTemplates ?? this.filteredTemplates,
      categories: categories ?? this.categories,
      selectedTemplate: selectedTemplate ?? this.selectedTemplate,
      previewTemplate: previewTemplate,
      selectedCategory: selectedCategory,
      showOnlyFreeTemplates: showOnlyFreeTemplates ?? this.showOnlyFreeTemplates,
      searchQuery: searchQuery ?? this.searchQuery,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        templates,
        filteredTemplates,
        categories,
        selectedTemplate,
        previewTemplate,
        selectedCategory,
        showOnlyFreeTemplates,
        searchQuery,
        isLoading,
        errorMessage,
      ];
}

class TemplateCubit extends Cubit<TemplateState> {
  final RemoteConfigCubit? _remoteConfigCubit;

  TemplateCubit([this._remoteConfigCubit]) : super(const TemplateState());

  static const String _selectedTemplateKey = 'selected_template_id';

  void loadTemplates() {
    emit(state.copyWith(isLoading: true));

    try {
      final allTemplates = TemplateRepository.getAllTemplates();
      final visibleTemplates = _filterTemplatesByRemoteConfig(allTemplates);
      final categories = TemplateCategory.values;

      emit(state.copyWith(
        templates: visibleTemplates,
        filteredTemplates: visibleTemplates,
        categories: categories,
        isLoading: false,
      ));

      // Load saved template selection
      _loadSelectedTemplate();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load templates: $e',
      ));
    }
  }

  void selectTemplate(ResumeTemplateModel template) async {
    debugPrint('DEBUG: TemplateCubit.selectTemplate called with: ${template.name} (ID: ${template.id})');
    emit(state.copyWith(selectedTemplate: template));

    // Save selection to preferences
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_selectedTemplateKey, template.id);
      debugPrint('DEBUG: Template selection saved to SharedPreferences: ${template.id}');
    } catch (e) {
      // Silently fail - not critical
      // TODO: Use proper logging framework
      debugPrint('Failed to save template selection: $e');
    }
  }

  void filterByCategory(TemplateCategory? category) {
    if (category == null) {
      emit(state.copyWith(
        filteredTemplates: state.templates,
        selectedCategory: null,
      ));
    } else {
      final filtered = state.templates
          .where((template) => template.category == category)
          .toList();
      
      emit(state.copyWith(
        filteredTemplates: filtered,
        selectedCategory: category,
      ));
    }
  }

  void togglePremiumFilter() {
    final showOnlyFree = !state.showOnlyFreeTemplates;
    
    List<ResumeTemplateModel> filtered;
    if (showOnlyFree) {
      filtered = state.templates.where((template) => !template.isPremium).toList();
    } else {
      filtered = state.selectedCategory != null
          ? state.templates.where((template) => template.category == state.selectedCategory).toList()
          : state.templates;
    }
    
    emit(state.copyWith(
      filteredTemplates: filtered,
      showOnlyFreeTemplates: showOnlyFree,
    ));
  }

  void searchTemplates(String query) {
    if (query.isEmpty) {
      emit(state.copyWith(
        filteredTemplates: state.templates,
        searchQuery: '',
      ));
      return;
    }

    final filtered = state.templates.where((template) {
      final nameMatch = template.name.toLowerCase().contains(query.toLowerCase());
      final descriptionMatch = template.description.toLowerCase().contains(query.toLowerCase());
      final categoryMatch = template.category.name.toLowerCase().contains(query.toLowerCase());

      return nameMatch || descriptionMatch || categoryMatch;
    }).toList();

    emit(state.copyWith(
      filteredTemplates: filtered,
      searchQuery: query,
    ));
  }

  void clearFilters() {
    emit(state.copyWith(
      filteredTemplates: state.templates,
      selectedCategory: null,
      showOnlyFreeTemplates: false,
      searchQuery: '',
    ));
  }

  void _loadSelectedTemplate() async {
    debugPrint('DEBUG: _loadSelectedTemplate called');
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedTemplateId = prefs.getString(_selectedTemplateKey);
      debugPrint('DEBUG: Saved template ID from SharedPreferences: $savedTemplateId');

      if (savedTemplateId != null) {
        final template = TemplateRepository.getTemplateById(savedTemplateId);
        debugPrint('DEBUG: Loaded template from repository: ${template.name} (ID: ${template.id})');
        emit(state.copyWith(selectedTemplate: template));
      } else {
        // Use default template
        final defaultTemplate = TemplateRepository.getDefaultTemplate();
        debugPrint('DEBUG: Using default template: ${defaultTemplate.name} (ID: ${defaultTemplate.id})');
        emit(state.copyWith(selectedTemplate: defaultTemplate));
      }
    } catch (e) {
      debugPrint('DEBUG: Error loading template: $e');
      // Use default template if loading fails
      final defaultTemplate = TemplateRepository.getDefaultTemplate();
      debugPrint('DEBUG: Using default template due to error: ${defaultTemplate.name} (ID: ${defaultTemplate.id})');
      emit(state.copyWith(selectedTemplate: defaultTemplate));
    }
  }

  ResumeTemplateModel getCurrentTemplate() {
    final template = state.selectedTemplate ?? TemplateRepository.getDefaultTemplate();
    debugPrint('DEBUG: TemplateCubit.getCurrentTemplate returning: ${template.name} (ID: ${template.id})');
    return template;
  }

  void previewTemplate(ResumeTemplateModel template) {
    emit(state.copyWith(previewTemplate: template));
  }

  void closePreview() {
    emit(state.copyWith(previewTemplate: null));
  }

  /// Filter templates based on Remote Config visibility settings
  List<ResumeTemplateModel> _filterTemplatesByRemoteConfig(List<ResumeTemplateModel> templates) {
    if (_remoteConfigCubit == null) {
      return templates; // Return all templates if Remote Config is not available
    }

    return templates.where((template) {
      // Check if template is visible according to Remote Config
      final isVisible = _remoteConfigCubit?.isTemplateVisible(template.id) ?? true;

      // Check if premium templates are enabled
      if (template.isPremium) {
        final premiumEnabled = _remoteConfigCubit?.isFeatureEnabled('enable_premium_templates') ?? true;
        return isVisible && premiumEnabled;
      }

      return isVisible;
    }).toList();
  }

  /// Refresh templates with current Remote Config settings
  void refreshTemplatesWithRemoteConfig() {
    final allTemplates = TemplateRepository.getAllTemplates();
    final visibleTemplates = _filterTemplatesByRemoteConfig(allTemplates);

    emit(state.copyWith(
      templates: visibleTemplates,
      filteredTemplates: _applyCurrentFilters(visibleTemplates),
    ));
  }

  /// Apply current filters to the given templates
  List<ResumeTemplateModel> _applyCurrentFilters(List<ResumeTemplateModel> templates) {
    var filtered = templates;

    // Apply category filter
    if (state.selectedCategory != null) {
      filtered = filtered.where((template) => template.category == state.selectedCategory).toList();
    }

    // Apply premium filter
    if (state.showOnlyFreeTemplates) {
      filtered = filtered.where((template) => !template.isPremium).toList();
    }

    // Apply search filter
    if (state.searchQuery.isNotEmpty) {
      filtered = filtered.where((template) {
        final nameMatch = template.name.toLowerCase().contains(state.searchQuery.toLowerCase());
        final descriptionMatch = template.description.toLowerCase().contains(state.searchQuery.toLowerCase());
        final categoryMatch = template.category.name.toLowerCase().contains(state.searchQuery.toLowerCase());
        return nameMatch || descriptionMatch || categoryMatch;
      }).toList();
    }

    return filtered;
  }

  /// Check if premium templates are enabled via Remote Config
  bool get isPremiumEnabled {
    return _remoteConfigCubit?.isFeatureEnabled('enable_premium_templates') ?? true;
  }

  /// Get maximum number of free resumes from Remote Config
  int get maxFreeResumes {
    return _remoteConfigCubit?.getConfigValue('max_free_resumes', 3) ?? 3;
  }
}
