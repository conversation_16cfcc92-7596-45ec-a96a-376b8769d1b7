import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../cubits/remote_config/remote_config_cubit.dart';

class RemoteConfigTestPage extends StatelessWidget {
  const RemoteConfigTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Remote Config Test'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<RemoteConfigCubit>().fetchConfig();
            },
          ),
        ],
      ),
      body: BlocBuilder<RemoteConfigCubit, RemoteConfigState>(
        builder: (context, state) {
          if (state is RemoteConfigLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is RemoteConfigError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading config',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<RemoteConfigCubit>().fetchConfig();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state is RemoteConfigLoaded) {
            final config = state.config;
            final cubit = context.read<RemoteConfigCubit>();

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Templates Configuration',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 16),
                          _buildConfigRow(
                            'Templates Enabled',
                            config.enableTemplates,
                            Icons.file_present_outlined,
                          ),
                          _buildConfigRow(
                            'Premium Templates',
                            config.enablePremiumTemplates,
                            Icons.star,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Ads Configuration',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 16),
                          _buildConfigRow(
                            'Banner Ads',
                            config.showBannerAds,
                            Icons.ad_units,
                          ),
                          _buildConfigRow(
                            'Interstitial Ads',
                            config.showInterstitialAds,
                            Icons.fullscreen,
                          ),
                          _buildConfigRow(
                            'Ad Frequency',
                            '${config.adFrequency}',
                            Icons.repeat,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Feature Toggles',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 16),
                          _buildConfigRow(
                            'Dark Mode',
                            config.enableDarkMode,
                            Icons.dark_mode,
                          ),
                          _buildConfigRow(
                            'Google Sign In',
                            config.enableGoogleSignIn,
                            Icons.login,
                          ),
                          _buildConfigRow(
                            'Apple Sign In',
                            config.enableAppleSignIn,
                            Icons.apple,
                          ),
                          _buildConfigRow(
                            'Analytics',
                            config.enableAnalytics,
                            Icons.analytics,
                          ),
                          _buildConfigRow(
                            'Push Notifications',
                            config.enablePushNotifications,
                            Icons.notifications,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'App Status',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 16),
                          _buildConfigRow(
                            'Maintenance Mode',
                            config.maintenanceMode,
                            Icons.construction,
                          ),
                          _buildConfigRow(
                            'Force Update',
                            config.forceUpdate,
                            Icons.system_update,
                          ),
                          _buildConfigRow(
                            'App Version',
                            config.appVersion,
                            Icons.info,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        context.read<RemoteConfigCubit>().fetchConfig();
                      },
                      child: const Text('Refresh Config'),
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: () {
                        // Test the templates enabled functionality
                        final templatesEnabled = cubit.isTemplatesEnabled();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Templates are ${templatesEnabled ? 'ENABLED' : 'DISABLED'}',
                            ),
                            backgroundColor: templatesEnabled 
                                ? Colors.green 
                                : Colors.orange,
                          ),
                        );
                      },
                      child: const Text('Test Templates Status'),
                    ),
                  ),
                ],
              ),
            );
          }

          return const Center(
            child: Text('Unknown state'),
          );
        },
      ),
    );
  }

  Widget _buildConfigRow(String label, dynamic value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(label),
          ),
          if (value is bool)
            Icon(
              value ? Icons.check_circle : Icons.cancel,
              color: value ? Colors.green : Colors.red,
              size: 20,
            )
          else
            Text(
              value.toString(),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
        ],
      ),
    );
  }
}
