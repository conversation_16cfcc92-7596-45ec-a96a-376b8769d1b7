import 'dart:convert';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import '../models/remote_config_model.dart';

class FirebaseRemoteConfigDataSource {
  final FirebaseRemoteConfig _remoteConfig;

  FirebaseRemoteConfigDataSource(this._remoteConfig);

  /// Initialize Remote Config with default values and fetch settings
  Future<void> initialize() async {
    try {
      // Set default values
      await _remoteConfig.setDefaults(_getDefaultValues());

      // Configure settings
      await _remoteConfig.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(hours: 1),
        ),
      );

      // Fetch and activate
      await _remoteConfig.fetchAndActivate();
    } catch (e) {
      // Log error but don't throw - use defaults
      print('Remote Config initialization failed: $e');
    }
  }

  /// Fetch latest config from Firebase
  Future<bool> fetchConfig() async {
    try {
      await _remoteConfig.fetchAndActivate();
      return true;
    } catch (e) {
      print('Failed to fetch remote config: $e');
      return false;
    }
  }

  /// Get current Remote Config as model
  RemoteConfigModel getConfig() {
    try {
      return RemoteConfigModel(
        showBannerAds: _remoteConfig.getBool('show_banner_ads'),
        showInterstitialAds: _remoteConfig.getBool('show_interstitial_ads'),
        enableTemplates: _remoteConfig.getBool('enable_templates'),
        enablePremiumTemplates: _remoteConfig.getBool('enable_premium_templates'),
        visibleTemplateIds: _getStringList('visible_template_ids'),
        maxFreeResumes: _remoteConfig.getInt('max_free_resumes'),
        enableDarkMode: _remoteConfig.getBool('enable_dark_mode'),
        enableGoogleSignIn: _remoteConfig.getBool('enable_google_sign_in'),
        enableAppleSignIn: _remoteConfig.getBool('enable_apple_sign_in'),
        supportEmail: _remoteConfig.getString('support_email'),
        privacyPolicyUrl: _remoteConfig.getString('privacy_policy_url'),
        termsOfServiceUrl: _remoteConfig.getString('terms_of_service_url'),
        enableAnalytics: _remoteConfig.getBool('enable_analytics'),
        enableCrashlytics: _remoteConfig.getBool('enable_crashlytics'),
        adFrequency: _remoteConfig.getInt('ad_frequency'),
        enablePushNotifications: _remoteConfig.getBool('enable_push_notifications'),
        appVersion: _remoteConfig.getString('app_version'),
        forceUpdate: _remoteConfig.getBool('force_update'),
        updateMessage: _remoteConfig.getString('update_message'),
        maintenanceMode: _remoteConfig.getBool('maintenance_mode'),
        maintenanceMessage: _remoteConfig.getString('maintenance_message'),
      );
    } catch (e) {
      print('Error getting remote config: $e');
      return const RemoteConfigModel(); // Return defaults
    }
  }

  /// Get specific boolean value
  bool getBool(String key) {
    try {
      return _remoteConfig.getBool(key);
    } catch (e) {
      print('Error getting bool value for $key: $e');
      return _getDefaultValues()[key] as bool? ?? false;
    }
  }

  /// Get specific string value
  String getString(String key) {
    try {
      return _remoteConfig.getString(key);
    } catch (e) {
      print('Error getting string value for $key: $e');
      return _getDefaultValues()[key] as String? ?? '';
    }
  }

  /// Get specific int value
  int getInt(String key) {
    try {
      return _remoteConfig.getInt(key);
    } catch (e) {
      print('Error getting int value for $key: $e');
      return _getDefaultValues()[key] as int? ?? 0;
    }
  }

  /// Get string list from JSON string
  List<String> _getStringList(String key) {
    try {
      final jsonString = _remoteConfig.getString(key);
      if (jsonString.isEmpty) return [];
      
      final decoded = jsonDecode(jsonString);
      if (decoded is List) {
        return decoded.cast<String>();
      }
      return [];
    } catch (e) {
      print('Error parsing string list for $key: $e');
      return [];
    }
  }

  /// Check if template is visible
  bool isTemplateVisible(String templateId) {
    final visibleTemplates = _getStringList('visible_template_ids');
    return visibleTemplates.isEmpty || visibleTemplates.contains(templateId);
  }

  /// Check if ads should be shown
  bool shouldShowBannerAds() => getBool('show_banner_ads');
  bool shouldShowInterstitialAds() => getBool('show_interstitial_ads');

  /// Check if feature is enabled
  bool isFeatureEnabled(String featureKey) => getBool(featureKey);

  /// Get ad frequency
  int getAdFrequency() => getInt('ad_frequency');

  /// Check if app is in maintenance mode
  bool isMaintenanceMode() => getBool('maintenance_mode');

  /// Check if force update is required
  bool isForceUpdateRequired() => getBool('force_update');

  /// Get default values for Remote Config
  Map<String, dynamic> _getDefaultValues() {
    return {
      'show_banner_ads': true,
      'show_interstitial_ads': true,
      'enable_templates': true,
      'enable_premium_templates': true,
      'visible_template_ids': '[]', // JSON string of template IDs
      'max_free_resumes': 3,
      'enable_dark_mode': true,
      'enable_google_sign_in': true,
      'enable_apple_sign_in': true,
      'support_email': '<EMAIL>',
      'privacy_policy_url': '',
      'terms_of_service_url': '',
      'enable_analytics': true,
      'enable_crashlytics': true,
      'ad_frequency': 3,
      'enable_push_notifications': true,
      'app_version': '1.0.0',
      'force_update': false,
      'update_message': '',
      'maintenance_mode': false,
      'maintenance_message': '',
    };
  }

  /// Listen to config updates
  Stream<RemoteConfigUpdate> get onConfigUpdated => _remoteConfig.onConfigUpdated;
}
