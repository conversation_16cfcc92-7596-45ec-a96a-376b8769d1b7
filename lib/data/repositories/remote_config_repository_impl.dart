import '../../domain/repositories/remote_config_repository.dart';
import '../datasources/firebase_remote_config_datasource.dart';
import '../models/remote_config_model.dart';

class RemoteConfigRepositoryImpl implements RemoteConfigRepository {
  final FirebaseRemoteConfigDataSource _dataSource;

  RemoteConfigRepositoryImpl(this._dataSource);

  @override
  Future<void> initialize() async {
    await _dataSource.initialize();
  }

  @override
  Future<bool> fetchConfig() async {
    return await _dataSource.fetchConfig();
  }

  @override
  RemoteConfigModel getConfig() {
    return _dataSource.getConfig();
  }

  @override
  bool getBool(String key) {
    return _dataSource.getBool(key);
  }

  @override
  String getString(String key) {
    return _dataSource.getString(key);
  }

  @override
  int getInt(String key) {
    return _dataSource.getInt(key);
  }

  @override
  bool isTemplateVisible(String templateId) {
    return _dataSource.isTemplateVisible(templateId);
  }

  @override
  bool shouldShowBannerAds() {
    return _dataSource.shouldShowBannerAds();
  }

  @override
  bool shouldShowInterstitialAds() {
    return _dataSource.shouldShowInterstitialAds();
  }

  @override
  bool isFeatureEnabled(String featureKey) {
    return _dataSource.isFeatureEnabled(featureKey);
  }

  @override
  int getAdFrequency() {
    return _dataSource.getAdFrequency();
  }

  @override
  bool isMaintenanceMode() {
    return _dataSource.isMaintenanceMode();
  }

  @override
  bool isForceUpdateRequired() {
    return _dataSource.isForceUpdateRequired();
  }

  @override
  Stream<void> get onConfigUpdated =>
      _dataSource.onConfigUpdated.map((_) {});
}
