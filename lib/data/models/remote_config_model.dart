import 'package:equatable/equatable.dart';

/// Model class for Remote Config settings
class RemoteConfigModel extends Equatable {
  final bool showBannerAds;
  final bool showInterstitialAds;
  final bool enablePremiumTemplates;
  final List<String> visibleTemplateIds;
  final int maxFreeResumes;
  final bool enableDarkMode;
  final bool enableGoogleSignIn;
  final bool enableAppleSignIn;
  final String supportEmail;
  final String privacyPolicyUrl;
  final String termsOfServiceUrl;
  final bool enableAnalytics;
  final bool enableCrashlytics;
  final int adFrequency;
  final bool enablePushNotifications;
  final String appVersion;
  final bool forceUpdate;
  final String updateMessage;
  final bool maintenanceMode;
  final String maintenanceMessage;

  const RemoteConfigModel({
    this.showBannerAds = true,
    this.showInterstitialAds = true,
    this.enablePremiumTemplates = true,
    this.visibleTemplateIds = const [],
    this.maxFreeResumes = 3,
    this.enableDarkMode = true,
    this.enableGoogleSignIn = true,
    this.enableAppleSignIn = true,
    this.supportEmail = '<EMAIL>',
    this.privacyPolicyUrl = '',
    this.termsOfServiceUrl = '',
    this.enableAnalytics = true,
    this.enableCrashlytics = true,
    this.adFrequency = 3,
    this.enablePushNotifications = true,
    this.appVersion = '1.0.0',
    this.forceUpdate = false,
    this.updateMessage = '',
    this.maintenanceMode = false,
    this.maintenanceMessage = '',
  });

  factory RemoteConfigModel.fromMap(Map<String, dynamic> map) {
    return RemoteConfigModel(
      showBannerAds: map['show_banner_ads'] ?? true,
      showInterstitialAds: map['show_interstitial_ads'] ?? true,
      enablePremiumTemplates: map['enable_premium_templates'] ?? true,
      visibleTemplateIds: List<String>.from(map['visible_template_ids'] ?? []),
      maxFreeResumes: map['max_free_resumes'] ?? 3,
      enableDarkMode: map['enable_dark_mode'] ?? true,
      enableGoogleSignIn: map['enable_google_sign_in'] ?? true,
      enableAppleSignIn: map['enable_apple_sign_in'] ?? true,
      supportEmail: map['support_email'] ?? '<EMAIL>',
      privacyPolicyUrl: map['privacy_policy_url'] ?? '',
      termsOfServiceUrl: map['terms_of_service_url'] ?? '',
      enableAnalytics: map['enable_analytics'] ?? true,
      enableCrashlytics: map['enable_crashlytics'] ?? true,
      adFrequency: map['ad_frequency'] ?? 3,
      enablePushNotifications: map['enable_push_notifications'] ?? true,
      appVersion: map['app_version'] ?? '1.0.0',
      forceUpdate: map['force_update'] ?? false,
      updateMessage: map['update_message'] ?? '',
      maintenanceMode: map['maintenance_mode'] ?? false,
      maintenanceMessage: map['maintenance_message'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'show_banner_ads': showBannerAds,
      'show_interstitial_ads': showInterstitialAds,
      'enable_premium_templates': enablePremiumTemplates,
      'visible_template_ids': visibleTemplateIds,
      'max_free_resumes': maxFreeResumes,
      'enable_dark_mode': enableDarkMode,
      'enable_google_sign_in': enableGoogleSignIn,
      'enable_apple_sign_in': enableAppleSignIn,
      'support_email': supportEmail,
      'privacy_policy_url': privacyPolicyUrl,
      'terms_of_service_url': termsOfServiceUrl,
      'enable_analytics': enableAnalytics,
      'enable_crashlytics': enableCrashlytics,
      'ad_frequency': adFrequency,
      'enable_push_notifications': enablePushNotifications,
      'app_version': appVersion,
      'force_update': forceUpdate,
      'update_message': updateMessage,
      'maintenance_mode': maintenanceMode,
      'maintenance_message': maintenanceMessage,
    };
  }

  RemoteConfigModel copyWith({
    bool? showBannerAds,
    bool? showInterstitialAds,
    bool? enablePremiumTemplates,
    List<String>? visibleTemplateIds,
    int? maxFreeResumes,
    bool? enableDarkMode,
    bool? enableGoogleSignIn,
    bool? enableAppleSignIn,
    String? supportEmail,
    String? privacyPolicyUrl,
    String? termsOfServiceUrl,
    bool? enableAnalytics,
    bool? enableCrashlytics,
    int? adFrequency,
    bool? enablePushNotifications,
    String? appVersion,
    bool? forceUpdate,
    String? updateMessage,
    bool? maintenanceMode,
    String? maintenanceMessage,
  }) {
    return RemoteConfigModel(
      showBannerAds: showBannerAds ?? this.showBannerAds,
      showInterstitialAds: showInterstitialAds ?? this.showInterstitialAds,
      enablePremiumTemplates: enablePremiumTemplates ?? this.enablePremiumTemplates,
      visibleTemplateIds: visibleTemplateIds ?? this.visibleTemplateIds,
      maxFreeResumes: maxFreeResumes ?? this.maxFreeResumes,
      enableDarkMode: enableDarkMode ?? this.enableDarkMode,
      enableGoogleSignIn: enableGoogleSignIn ?? this.enableGoogleSignIn,
      enableAppleSignIn: enableAppleSignIn ?? this.enableAppleSignIn,
      supportEmail: supportEmail ?? this.supportEmail,
      privacyPolicyUrl: privacyPolicyUrl ?? this.privacyPolicyUrl,
      termsOfServiceUrl: termsOfServiceUrl ?? this.termsOfServiceUrl,
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
      enableCrashlytics: enableCrashlytics ?? this.enableCrashlytics,
      adFrequency: adFrequency ?? this.adFrequency,
      enablePushNotifications: enablePushNotifications ?? this.enablePushNotifications,
      appVersion: appVersion ?? this.appVersion,
      forceUpdate: forceUpdate ?? this.forceUpdate,
      updateMessage: updateMessage ?? this.updateMessage,
      maintenanceMode: maintenanceMode ?? this.maintenanceMode,
      maintenanceMessage: maintenanceMessage ?? this.maintenanceMessage,
    );
  }

  @override
  List<Object?> get props => [
        showBannerAds,
        showInterstitialAds,
        enablePremiumTemplates,
        visibleTemplateIds,
        maxFreeResumes,
        enableDarkMode,
        enableGoogleSignIn,
        enableAppleSignIn,
        supportEmail,
        privacyPolicyUrl,
        termsOfServiceUrl,
        enableAnalytics,
        enableCrashlytics,
        adFrequency,
        enablePushNotifications,
        appVersion,
        forceUpdate,
        updateMessage,
        maintenanceMode,
        maintenanceMessage,
      ];
}
