# 📱 Google Mobile Ads + Remote Config Setup Guide

## 🎯 **What's Been Implemented**

✅ **Google Mobile Ads Integration**
- Banner ads in My Resumes page
- Interstitial ads in Resume Builder page
- Remote Config controls for ad display

✅ **Remote Config Parameters**
- `show_banner_ads` - Controls banner ad visibility
- `show_interstitial_ads` - Controls interstitial ad visibility  
- `enable_premium_templates` - Controls premium template access

✅ **Test Pages**
- `/ads-test` - Test ad functionality
- `/remote-config-demo` - Test Remote Config features

## 🚀 **Quick Setup Steps**

### Step 1: Firebase Console Setup

1. **Open Firebase Console:**
   ```
   https://console.firebase.google.com/project/resume-d24cb/remoteconfig
   ```

2. **Add these 3 parameters:**

   | Parameter | Type | Default Value |
   |-----------|------|---------------|
   | `show_banner_ads` | Boolean | `true` |
   | `show_interstitial_ads` | Boolean | `true` |
   | `enable_premium_templates` | Boolean | `true` |

3. **Publish the configuration**

### Step 2: Test the Implementation

1. **Run your app:**
   ```bash
   flutter run
   ```

2. **Test ads functionality:**
   - Navigate to `/ads-test` route
   - Check Remote Config status
   - Test banner and interstitial ads

3. **Test in actual pages:**
   - Go to "My Resumes" page → See banner ad at top
   - Go to "Resume Builder" page → See interstitial ad after 2 seconds

### Step 3: Control Ads Remotely

1. **In Firebase Console:**
   - Toggle `show_banner_ads` to `false`
   - Publish changes

2. **In your app:**
   - Refresh Remote Config (tap refresh button)
   - Banner ads will disappear immediately

3. **Toggle back to `true`:**
   - Banner ads will reappear

## 🎛️ **How It Works**

### Banner Ads (My Resumes Page)
```dart
// Automatically checks Remote Config
const BannerAdWidget()

// Shows at top of resumes list
// Hides when show_banner_ads = false
```

### Interstitial Ads (Resume Builder Page)
```dart
// Loads when page opens
_loadInterstitialAd()

// Shows after 2 seconds if enabled
// Controlled by show_interstitial_ads
```

### Remote Config Integration
```dart
// Check if ads should be shown
final cubit = context.read<RemoteConfigCubit>();
bool showBanner = cubit.shouldShowBannerAds();
bool showInterstitial = cubit.shouldShowInterstitialAds();
```

## 🧪 **Testing Scenarios**

### Scenario 1: Enable All Ads
```
show_banner_ads: true
show_interstitial_ads: true
```
**Result:** Both banner and interstitial ads show

### Scenario 2: Disable Banner Ads Only
```
show_banner_ads: false
show_interstitial_ads: true
```
**Result:** No banner ads, interstitial ads still show

### Scenario 3: Disable All Ads
```
show_banner_ads: false
show_interstitial_ads: false
```
**Result:** No ads shown anywhere

### Scenario 4: Premium Templates Control
```
enable_premium_templates: false
```
**Result:** Premium templates hidden from template selection

## 📱 **Ad Placement Details**

### Banner Ad in My Resumes Page
- **Location:** Top of the resumes list
- **Size:** Standard banner (320x50)
- **Behavior:** 
  - Shows when `show_banner_ads = true`
  - Hides when `show_banner_ads = false`
  - Automatically refreshes on Remote Config changes

### Interstitial Ad in Resume Builder Page
- **Trigger:** Page load + 2 second delay
- **Frequency:** Once per page visit
- **Behavior:**
  - Loads when `show_interstitial_ads = true`
  - Skips loading when `show_interstitial_ads = false`
  - Auto-loads next ad after dismissal

## 🔧 **Customization Options**

### Change Ad Timing
```dart
// In resume_builder_page.dart, line ~57
Future.delayed(const Duration(seconds: 2), () {
  // Change seconds to adjust delay
```

### Change Banner Ad Position
```dart
// In my_resumes_page.dart, line ~244
if (index == 0) {
  return const BannerAdWidget();
}
// Move this block to change position
```

### Add More Ad Placements
```dart
// Use BannerAdWidget anywhere:
const BannerAdWidget(
  margin: EdgeInsets.all(16),
  backgroundColor: Colors.grey.shade100,
)
```

## 🎯 **Production Setup**

### Replace Test Ad Unit IDs
In `lib/core/services/ad_service.dart`:

```dart
// Replace these with your real AdMob unit IDs
static const String _bannerAdUnitId = Platform.isAndroid
    ? 'ca-app-pub-YOUR_PUBLISHER_ID/YOUR_BANNER_ID'
    : 'ca-app-pub-YOUR_PUBLISHER_ID/YOUR_IOS_BANNER_ID';

static const String _interstitialAdUnitId = Platform.isAndroid
    ? 'ca-app-pub-YOUR_PUBLISHER_ID/YOUR_INTERSTITIAL_ID'
    : 'ca-app-pub-YOUR_PUBLISHER_ID/YOUR_IOS_INTERSTITIAL_ID';
```

### Get Your Ad Unit IDs
1. Go to [AdMob Console](https://apps.admob.com/)
2. Create your app
3. Create ad units for banner and interstitial
4. Copy the ad unit IDs

## 🚨 **Important Notes**

### Test Ads
- Currently using Google's test ad unit IDs
- Test ads will show "Test Ad" label
- Safe to use during development

### Real Ads
- Replace test IDs with real ones for production
- Real ads will show actual advertisements
- Ensure compliance with app store policies

### Remote Config
- Changes take effect immediately after refresh
- No app update required to control ads
- Perfect for A/B testing ad strategies

## 🎉 **You're Ready!**

Your ad system is now fully functional with Remote Config controls. You can:

✅ Show/hide banner ads instantly
✅ Enable/disable interstitial ads remotely  
✅ Control premium template access
✅ Test everything with the `/ads-test` page
✅ A/B test different ad configurations

Start experimenting with different settings in Firebase Console to see real-time changes in your app!
