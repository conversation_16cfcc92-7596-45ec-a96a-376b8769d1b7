# 🔥 Firebase Remote Config Service Guide

## Overview

This guide explains how to use the Firebase Remote Config service that has been implemented in your Resume Builder app. The service allows you to remotely control various aspects of your app without requiring app updates.

## Features

### 🎛️ Core Features
- **Template Visibility Control** - Show/hide specific resume templates
- **Ad Display Management** - Toggle banner and interstitial ads
- **Feature Flags** - Enable/disable app features remotely
- **App Maintenance Mode** - Put app in maintenance mode
- **Force Update Control** - Require users to update the app
- **Configuration Management** - Manage app settings remotely

### 📱 Available Configuration Options

| Key | Type | Default | Description |
|-----|------|---------|-------------|
| `show_banner_ads` | Boolean | `true` | Show banner advertisements |
| `show_interstitial_ads` | Boolean | `true` | Show interstitial advertisements |
| `enable_premium_templates` | Boolean | `true` | Enable premium template features |
| `visible_template_ids` | JSON Array | `[]` | List of visible template IDs (empty = all visible) |
| `max_free_resumes` | Number | `3` | Maximum number of free resumes |
| `enable_dark_mode` | Boolean | `true` | Enable dark mode option |
| `enable_google_sign_in` | Boolean | `true` | Enable Google Sign-In |
| `enable_apple_sign_in` | Boolean | `true` | Enable Apple Sign-In |
| `support_email` | String | `<EMAIL>` | Support contact email |
| `privacy_policy_url` | String | `""` | Privacy policy URL |
| `terms_of_service_url` | String | `""` | Terms of service URL |
| `enable_analytics` | Boolean | `true` | Enable analytics tracking |
| `enable_crashlytics` | Boolean | `true` | Enable crash reporting |
| `ad_frequency` | Number | `3` | Show ads every N actions |
| `enable_push_notifications` | Boolean | `true` | Enable push notifications |
| `app_version` | String | `1.0.0` | Current app version |
| `force_update` | Boolean | `false` | Force users to update |
| `update_message` | String | `""` | Message shown for force update |
| `maintenance_mode` | Boolean | `false` | Put app in maintenance mode |
| `maintenance_message` | String | `""` | Message shown during maintenance |

## 🚀 Usage Examples

### 1. Basic Configuration Access

```dart
// Get the Remote Config cubit
final remoteConfigCubit = context.read<RemoteConfigCubit>();

// Check if feature is enabled
bool isPremiumEnabled = remoteConfigCubit.isFeatureEnabled('enable_premium_templates');

// Get configuration values
int maxResumes = remoteConfigCubit.getConfigValue('max_free_resumes', 3);
String supportEmail = remoteConfigCubit.getConfigValue('support_email', '<EMAIL>');
```

### 2. Template Visibility Control

```dart
// Filter templates based on visibility settings
TemplateFilter(
  templates: allTemplates,
  getTemplateId: (template) => template.id,
  builder: (visibleTemplates) {
    return ListView.builder(
      itemCount: visibleTemplates.length,
      itemBuilder: (context, index) {
        return TemplateCard(template: visibleTemplates[index]);
      },
    );
  },
)
```

### 3. Ad Display Control

```dart
// Show banner ad only if enabled
AdWidget(
  adType: AdType.banner,
  child: BannerAdWidget(),
  fallback: SizedBox.shrink(), // Hide if ads disabled
)

// Check before showing interstitial ad
if (remoteConfigCubit.shouldShowInterstitialAds()) {
  showInterstitialAd();
}
```

### 4. Feature Flags

```dart
// Conditionally show features
FeatureFlag(
  featureKey: 'enable_premium_templates',
  child: PremiumTemplatesSection(),
  fallback: ComingSoonWidget(),
)
```

### 5. App Status Management

```dart
// Wrap your app with maintenance and update checks
MaintenanceCheck(
  child: ForceUpdateCheck(
    child: YourMainApp(),
  ),
)
```

## 🛠️ Setup Instructions

### 1. Firebase Console Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project (`resume-d24cb`)
3. Navigate to **Remote Config** in the left sidebar
4. Click **Create configuration**

### 2. Add Configuration Parameters

For each parameter you want to control, add it to Remote Config:

1. Click **Add parameter**
2. Enter the parameter key (e.g., `show_banner_ads`)
3. Set the default value
4. Optionally add conditions for different user segments
5. Click **Publish changes**

### 3. Example Configuration Setup

Here's how to set up common configurations:

#### Template Visibility
```json
{
  "parameter_key": "visible_template_ids",
  "default_value": "[\"template_1\", \"template_2\", \"template_3\"]",
  "description": "List of visible template IDs"
}
```

#### Ad Control
```json
{
  "parameter_key": "show_banner_ads",
  "default_value": true,
  "description": "Show banner advertisements"
}
```

#### Feature Flags
```json
{
  "parameter_key": "enable_premium_templates",
  "default_value": true,
  "description": "Enable premium template features"
}
```

## 🎯 Advanced Usage

### Custom Configuration Values

```dart
// Get any custom configuration value with type safety
String customValue = remoteConfigCubit.getConfigValue<String>('custom_feature', 'default');
int customNumber = remoteConfigCubit.getConfigValue<int>('custom_limit', 10);
bool customFlag = remoteConfigCubit.getConfigValue<bool>('custom_enabled', false);
```

### Real-time Updates

The service automatically fetches updates every hour, but you can manually refresh:

```dart
// Manually fetch latest configuration
await remoteConfigCubit.fetchConfig();
```

### Error Handling

The service gracefully handles errors and falls back to default values:

```dart
RemoteConfigBuilder(
  builder: (context, config) {
    // This will always receive a valid config object
    return YourWidget(config: config);
  },
  loading: CircularProgressIndicator(),
  error: Text('Configuration temporarily unavailable'),
)
```

## 🔧 Troubleshooting

### Common Issues

1. **Configuration not updating**
   - Check if you published changes in Firebase Console
   - Wait for the fetch interval (1 hour) or manually refresh
   - Verify your Firebase project ID

2. **Default values not working**
   - Ensure default values are set in the data source
   - Check for typos in parameter keys

3. **App crashes on Remote Config access**
   - Verify Firebase is properly initialized
   - Check dependency injection setup

### Debug Mode

To test configurations immediately in debug mode:

```dart
// In debug mode, set minimum fetch interval to 0
await FirebaseRemoteConfig.instance.setConfigSettings(
  RemoteConfigSettings(
    fetchTimeout: const Duration(minutes: 1),
    minimumFetchInterval: Duration.zero, // For debug only
  ),
);
```

## 📊 Best Practices

1. **Use meaningful parameter names** - Use descriptive keys like `enable_premium_templates` instead of `flag1`

2. **Set sensible defaults** - Always provide fallback values that keep your app functional

3. **Test thoroughly** - Test both enabled and disabled states of features

4. **Gradual rollouts** - Use conditions to gradually roll out features to user segments

5. **Monitor usage** - Track how configuration changes affect user behavior

6. **Document changes** - Keep track of what each parameter controls

## 🚀 Next Steps

1. Set up your Remote Config parameters in Firebase Console
2. Test the configuration changes in your app
3. Implement A/B testing using Remote Config conditions
4. Monitor app performance and user engagement
5. Use Remote Config for gradual feature rollouts

For more advanced features, consider implementing:
- User segmentation based on app version, location, or user properties
- A/B testing for different configurations
- Analytics integration to measure impact of configuration changes
